﻿/**
** =====================================================================================
**
**       文件名称: memory.cpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强内存管理系统 - 现代化C++17内存管理框架 （实现文件）
**
**       主要功能:
**       - 高性能内存分配与释放管理
**       - 对齐内存分配支持（SSE/AVX优化）
**       - 零填充与安全内存操作
**       - 内存使用统计与监控
**       - 内存泄漏检测与调试支持
**       - 异常安全的内存管理
**       - 智能内存池集成
**
**       技术特性:
**       - 采用现代C++17标准与RAII设计
**       - 支持多种对齐方式（16/32/64字节对齐）
**       - 原子操作保证线程安全统计
**       - 异常安全保证与错误恢复机制
**       - 高性能内存分配算法
**       - 智能指针兼容设计
**       - 内存使用模式分析
**
**       更新记录:
**       2025-07-31 v2.0.0.0 : 1. 增强内存管理系统
**                             2. 添加对齐分配支持
**                             3. 实现安全内存操作
**                             4. 添加内存统计功能
**
** =====================================================================================
*/
#include "pch.h"
#include "memory.h"
#include "Exception.h"

namespace HHBUI
{
	// 全局内存统计信息
	static MemoryStats g_memory_stats;
	LPVOID ExMemAlloc(size_t size)
	{
		if (size == 0) return nullptr;

		LPVOID ptr = LocalAlloc(LMEM_FIXED | LMEM_ZEROINIT, size);
		if (ptr)
		{
			// 更新统计信息
			g_memory_stats.total_allocated.fetch_add(size, std::memory_order_relaxed);
			g_memory_stats.allocation_count.fetch_add(1, std::memory_order_relaxed);

			size_t current = g_memory_stats.current_usage.fetch_add(size, std::memory_order_relaxed) + size;
			size_t peak = g_memory_stats.peak_usage.load(std::memory_order_relaxed);
			while (current > peak && !g_memory_stats.peak_usage.compare_exchange_weak(peak, current, std::memory_order_relaxed)) {}
		}
		return ptr;
	}

	LPVOID ExMemReAlloc(LPVOID ptr, size_t new_size)
	{
		// 如果ptr为空,直接分配新内存
		if (!ptr) { return ExMemAlloc(new_size); }

		//获取一下旧内存尺寸
		size_t old_size = LocalSize(ptr);
		LPVOID new_ptr = nullptr;

		//如果尺寸一样就不分配了
		if (old_size == new_size) { return ptr; }

		//判断一下,如果是缩小,则直接调用LocalReAlloc缩小
		if (old_size > new_size) {
			new_ptr = LocalReAlloc(ptr, new_size, LMEM_FIXED | LMEM_ZEROINIT);
		}

		//如果扩大内存或LocalReAlloc执行失败,则手动重新分配
		if (new_ptr == nullptr) {

			//申请新内存
			new_ptr = ExMemAlloc(new_size);
			if (!new_ptr) { return nullptr; }

			//从源内存拷贝数据到新内存头部
			memcpy(new_ptr, ptr, std::min(old_size, new_size));

			//释放旧内存
			ExMemFree(ptr);
		}

		//返回新内存
		return new_ptr;
	}

	void ExMemFree(LPVOID ptr)
	{
		if (ptr)
		{
			size_t size = LocalSize(ptr);
			LocalFree(ptr);

			// 更新统计信息
			g_memory_stats.total_freed.fetch_add(size, std::memory_order_relaxed);
			g_memory_stats.free_count.fetch_add(1, std::memory_order_relaxed);
			g_memory_stats.current_usage.fetch_sub(size, std::memory_order_relaxed);
		}
	}

	size_t ExMemGetSize(LPVOID ptr)
	{
		return ptr ? LocalSize(ptr) : 0;
	}

	LPVOID ExMemAllocAligned(size_t size, MemoryAlignment alignment)
	{
		if (size == 0) return nullptr;

		size_t align_bytes = static_cast<size_t>(alignment);

		// 计算需要的总大小（包括对齐填充和存储原始指针的空间）
		size_t total_size = size + align_bytes + sizeof(void*);

		LPVOID raw_ptr = ExMemAlloc(total_size);
		if (!raw_ptr) return nullptr;

		// 计算对齐后的地址
		uintptr_t raw_addr = reinterpret_cast<uintptr_t>(raw_ptr);
		uintptr_t aligned_addr = (raw_addr + sizeof(void*) + align_bytes - 1) & ~(align_bytes - 1);

		// 在对齐地址前存储原始指针
		void** aligned_ptr = reinterpret_cast<void**>(aligned_addr);
		*(aligned_ptr - 1) = raw_ptr;

		// 更新对齐分配统计
		g_memory_stats.alignment_count.fetch_add(1, std::memory_order_relaxed);

		return aligned_ptr;
	}

	void ExMemFreeAligned(LPVOID ptr)
	{
		if (ptr)
		{
			// 获取原始指针并释放
			void** aligned_ptr = static_cast<void**>(ptr);
			void* raw_ptr = *(aligned_ptr - 1);
			ExMemFree(raw_ptr);
		}
	}

	LPVOID ExMemAllocZero(size_t size)
	{
		// ExMemAlloc 已经使用 LMEM_ZEROINIT，所以直接调用即可
		return ExMemAlloc(size);
	}

	HRESULT ExMemCopySafe(LPVOID dest, size_t dest_size, LPCVOID src, size_t src_size)
	{
		if (!dest || !src) return E_POINTER;
		if (dest_size == 0 || src_size == 0) return E_INVALIDARG;

		size_t copy_size = std::min(dest_size, src_size);

		try
		{
			std::memcpy(dest, src, copy_size);
			return S_OK;
		}
		catch (...)
		{
			return E_FAIL;
		}
	}

	HRESULT ExMemSetSafe(LPVOID dest, size_t dest_size, int value)
	{
		if (!dest) return E_POINTER;
		if (dest_size == 0) return E_INVALIDARG;

		try
		{
			std::memset(dest, value, dest_size);
			return S_OK;
		}
		catch (...)
		{
			return E_FAIL;
		}
	}

	HRESULT ExMemZeroSafe(LPVOID dest, size_t dest_size)
	{
		return ExMemSetSafe(dest, dest_size, 0);
	}

	const MemoryStats& ExMemGetStats()
	{
		return g_memory_stats;
	}

	void ExMemResetStats()
	{
		g_memory_stats.total_allocated.store(0, std::memory_order_relaxed);
		g_memory_stats.total_freed.store(0, std::memory_order_relaxed);
		g_memory_stats.current_usage.store(0, std::memory_order_relaxed);
		g_memory_stats.peak_usage.store(0, std::memory_order_relaxed);
		g_memory_stats.allocation_count.store(0, std::memory_order_relaxed);
		g_memory_stats.free_count.store(0, std::memory_order_relaxed);
		g_memory_stats.alignment_count.store(0, std::memory_order_relaxed);
	}

	bool ExMemIsAligned(LPCVOID ptr, size_t alignment)
	{
		if (!ptr || alignment == 0) return false;
		return (reinterpret_cast<uintptr_t>(ptr) % alignment) == 0;
	}
}


