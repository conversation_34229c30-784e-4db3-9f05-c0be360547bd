/**
** =====================================================================================
**
**       文件名称: smart_ptr.hpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】现代化智能指针系统 - C++17标准智能指针框架 （声明文件）
**
**       主要功能:
**       - 完整的shared_ptr智能指针实现
**       - weak_ptr弱引用指针支持
**       - unique_ptr独占指针管理
**       - 线程安全的引用计数机制
**       - 异常安全的资源管理
**       - 自定义删除器支持
**       - 智能指针转换与兼容性
**
**       技术特性:
**       - 采用现代C++17标准设计
**       - 原子操作保证线程安全
**       - 异常安全保证与RAII管理
**       - 高性能引用计数算法
**       - 模板元编程优化
**       - 与标准库智能指针兼容
**       - 支持自定义内存分配器
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建现代化智能指针系统
**                             2. 实现shared_ptr与weak_ptr
**                             3. 添加unique_ptr支持
**                             4. 集成线程安全机制
**
** =====================================================================================
*/
#pragma once
#include <atomic>
#include <memory>
#include <functional>
#include <type_traits>
#include <utility>
#include "memory.h"
#include "Exception.h"

namespace HHBUI
{
	// 前向声明
	template<typename T> class ExSharedPtr;
	template<typename T> class ExWeakPtr;
	template<typename T, typename Deleter = std::default_delete<T>> class ExUniquePtr;

	namespace detail
	{
		// 引用计数控制块
		struct RefCountBase
		{
			std::atomic<long> shared_count{ 1 };
			std::atomic<long> weak_count{ 1 };

			virtual ~RefCountBase() = default;
			virtual void destroy_object() = 0;
			virtual void* get_deleter(const std::type_info& ti) noexcept = 0;

			void add_ref_shared() noexcept
			{
				shared_count.fetch_add(1, std::memory_order_relaxed);
			}

			void add_ref_weak() noexcept
			{
				weak_count.fetch_add(1, std::memory_order_relaxed);
			}

			void release_shared() noexcept
			{
				if (shared_count.fetch_sub(1, std::memory_order_acq_rel) == 1)
				{
					destroy_object();
					release_weak();
				}
			}

			void release_weak() noexcept
			{
				if (weak_count.fetch_sub(1, std::memory_order_acq_rel) == 1)
				{
					delete this;
				}
			}

			long use_count() const noexcept
			{
				return shared_count.load(std::memory_order_relaxed);
			}

			bool expired() const noexcept
			{
				return shared_count.load(std::memory_order_relaxed) == 0;
			}
		};

		// 默认删除器的引用计数控制块
		template<typename T>
		struct RefCountObj : public RefCountBase
		{
			T* ptr;

			explicit RefCountObj(T* p) : ptr(p) {}

			void destroy_object() override
			{
				delete ptr;
				ptr = nullptr;
			}

			void* get_deleter(const std::type_info& ti) noexcept override
			{
				return nullptr;
			}
		};

		// 自定义删除器的引用计数控制块
		template<typename T, typename Deleter>
		struct RefCountObjDeleter : public RefCountBase
		{
			T* ptr;
			Deleter deleter;

			RefCountObjDeleter(T* p, Deleter d) : ptr(p), deleter(std::move(d)) {}

			void destroy_object() override
			{
				deleter(ptr);
				ptr = nullptr;
			}

			void* get_deleter(const std::type_info& ti) noexcept override
			{
				if (ti == typeid(Deleter))
					return std::addressof(deleter);
				return nullptr;
			}
		};

		// make_shared的引用计数控制块
		template<typename T>
		struct RefCountInplace : public RefCountBase
		{
			alignas(T) char storage[sizeof(T)];

			template<typename... Args>
			RefCountInplace(Args&&... args)
			{
				new(storage) T(std::forward<Args>(args)...);
			}

			T* get_ptr() noexcept
			{
				return reinterpret_cast<T*>(storage);
			}

			void destroy_object() override
			{
				get_ptr()->~T();
			}

			void* get_deleter(const std::type_info& ti) noexcept override
			{
				return nullptr;
			}
		};
	}

	/**
	 * @brief 共享智能指针
	 * 支持多个指针共享同一个对象的所有权
	 */
	template<typename T>
	class ExSharedPtr
	{
	public:
		using element_type = std::remove_extent_t<T>;
		using weak_type = ExWeakPtr<T>;

		// 构造函数
		constexpr ExSharedPtr() noexcept : ptr_(nullptr), ref_count_(nullptr) {}
		constexpr ExSharedPtr(std::nullptr_t) noexcept : ptr_(nullptr), ref_count_(nullptr) {}

		template<typename Y>
		explicit ExSharedPtr(Y* p) : ptr_(p), ref_count_(nullptr)
		{
			static_assert(std::is_convertible_v<Y*, T*>, "Y* must be convertible to T*");
			try
			{
				ref_count_ = new detail::RefCountObj<Y>(p);
			}
			catch (...)
			{
				delete p;
				throw;
			}
		}

		template<typename Y, typename Deleter>
		ExSharedPtr(Y* p, Deleter d) : ptr_(p), ref_count_(nullptr)
		{
			static_assert(std::is_convertible_v<Y*, T*>, "Y* must be convertible to T*");
			try
			{
				ref_count_ = new detail::RefCountObjDeleter<Y, Deleter>(p, std::move(d));
			}
			catch (...)
			{
				d(p);
				throw;
			}
		}

		// 拷贝构造函数
		ExSharedPtr(const ExSharedPtr& other) noexcept : ptr_(other.ptr_), ref_count_(other.ref_count_)
		{
			if (ref_count_)
				ref_count_->add_ref_shared();
		}

		template<typename Y>
		ExSharedPtr(const ExSharedPtr<Y>& other) noexcept : ptr_(other.ptr_), ref_count_(other.ref_count_)
		{
			static_assert(std::is_convertible_v<Y*, T*>, "Y* must be convertible to T*");
			if (ref_count_)
				ref_count_->add_ref_shared();
		}

		// 移动构造函数
		ExSharedPtr(ExSharedPtr&& other) noexcept : ptr_(other.ptr_), ref_count_(other.ref_count_)
		{
			other.ptr_ = nullptr;
			other.ref_count_ = nullptr;
		}

		template<typename Y>
		ExSharedPtr(ExSharedPtr<Y>&& other) noexcept : ptr_(other.ptr_), ref_count_(other.ref_count_)
		{
			static_assert(std::is_convertible_v<Y*, T*>, "Y* must be convertible to T*");
			other.ptr_ = nullptr;
			other.ref_count_ = nullptr;
		}

		// 从weak_ptr构造
		template<typename Y>
		explicit ExSharedPtr(const ExWeakPtr<Y>& weak) : ptr_(nullptr), ref_count_(nullptr)
		{
			static_assert(std::is_convertible_v<Y*, T*>, "Y* must be convertible to T*");
			if (!weak.expired())
			{
				ptr_ = weak.ptr_;
				ref_count_ = weak.ref_count_;
				if (ref_count_)
					ref_count_->add_ref_shared();
			}
			else
			{
				throw_ex(E_FAIL, L"weak_ptr已过期");
			}
		}

		// 析构函数
		~ExSharedPtr()
		{
			if (ref_count_)
				ref_count_->release_shared();
		}

		// 赋值操作符
		ExSharedPtr& operator=(const ExSharedPtr& other) noexcept
		{
			ExSharedPtr(other).swap(*this);
			return *this;
		}

		template<typename Y>
		ExSharedPtr& operator=(const ExSharedPtr<Y>& other) noexcept
		{
			ExSharedPtr(other).swap(*this);
			return *this;
		}

		ExSharedPtr& operator=(ExSharedPtr&& other) noexcept
		{
			ExSharedPtr(std::move(other)).swap(*this);
			return *this;
		}

		template<typename Y>
		ExSharedPtr& operator=(ExSharedPtr<Y>&& other) noexcept
		{
			ExSharedPtr(std::move(other)).swap(*this);
			return *this;
		}

		// 访问操作符
		T& operator*() const noexcept
		{
			return *ptr_;
		}

		T* operator->() const noexcept
		{
			return ptr_;
		}

		T* get() const noexcept
		{
			return ptr_;
		}

		// 类型转换
		explicit operator bool() const noexcept
		{
			return ptr_ != nullptr;
		}

		// 引用计数
		long use_count() const noexcept
		{
			return ref_count_ ? ref_count_->use_count() : 0;
		}

		bool unique() const noexcept
		{
			return use_count() == 1;
		}

		// 重置
		void reset() noexcept
		{
			ExSharedPtr().swap(*this);
		}

		template<typename Y>
		void reset(Y* p)
		{
			ExSharedPtr(p).swap(*this);
		}

		template<typename Y, typename Deleter>
		void reset(Y* p, Deleter d)
		{
			ExSharedPtr(p, std::move(d)).swap(*this);
		}

		// 交换
		void swap(ExSharedPtr& other) noexcept
		{
			std::swap(ptr_, other.ptr_);
			std::swap(ref_count_, other.ref_count_);
		}

		// 获取删除器
		template<typename Deleter>
		Deleter* get_deleter() const noexcept
		{
			return ref_count_ ? static_cast<Deleter*>(ref_count_->get_deleter(typeid(Deleter))) : nullptr;
		}

	private:
		template<typename Y> friend class ExSharedPtr;
		template<typename Y> friend class ExWeakPtr;
		template<typename Y, typename... Args> friend ExSharedPtr<Y> ExMakeShared(Args&&... args);

		T* ptr_;
		detail::RefCountBase* ref_count_;

		// 内部构造函数（用于make_shared）
		template<typename Y>
		ExSharedPtr(detail::RefCountInplace<Y>* ref) : ptr_(ref->get_ptr()), ref_count_(ref) {}

		// 别名构造函数（用于类型转换）
		template<typename Y>
		ExSharedPtr(const ExSharedPtr<Y>& other, T* ptr) noexcept : ptr_(ptr), ref_count_(other.ref_count_)
		{
			if (ref_count_)
				ref_count_->add_ref_shared();
		}
	};

	/**
	 * @brief 弱引用智能指针
	 * 不拥有对象，但可以检测对象是否仍然存在
	 */
	template<typename T>
	class ExWeakPtr
	{
	public:
		using element_type = std::remove_extent_t<T>;

		// 构造函数
		constexpr ExWeakPtr() noexcept : ptr_(nullptr), ref_count_(nullptr) {}

		// 从shared_ptr构造
		template<typename Y>
		ExWeakPtr(const ExSharedPtr<Y>& shared) noexcept : ptr_(shared.ptr_), ref_count_(shared.ref_count_)
		{
			static_assert(std::is_convertible_v<Y*, T*>, "Y* must be convertible to T*");
			if (ref_count_)
				ref_count_->add_ref_weak();
		}

		// 拷贝构造函数
		ExWeakPtr(const ExWeakPtr& other) noexcept : ptr_(other.ptr_), ref_count_(other.ref_count_)
		{
			if (ref_count_)
				ref_count_->add_ref_weak();
		}

		template<typename Y>
		ExWeakPtr(const ExWeakPtr<Y>& other) noexcept : ptr_(other.ptr_), ref_count_(other.ref_count_)
		{
			static_assert(std::is_convertible_v<Y*, T*>, "Y* must be convertible to T*");
			if (ref_count_)
				ref_count_->add_ref_weak();
		}

		// 移动构造函数
		ExWeakPtr(ExWeakPtr&& other) noexcept : ptr_(other.ptr_), ref_count_(other.ref_count_)
		{
			other.ptr_ = nullptr;
			other.ref_count_ = nullptr;
		}

		template<typename Y>
		ExWeakPtr(ExWeakPtr<Y>&& other) noexcept : ptr_(other.ptr_), ref_count_(other.ref_count_)
		{
			static_assert(std::is_convertible_v<Y*, T*>, "Y* must be convertible to T*");
			other.ptr_ = nullptr;
			other.ref_count_ = nullptr;
		}

		// 析构函数
		~ExWeakPtr()
		{
			if (ref_count_)
				ref_count_->release_weak();
		}

		// 赋值操作符
		ExWeakPtr& operator=(const ExWeakPtr& other) noexcept
		{
			ExWeakPtr(other).swap(*this);
			return *this;
		}

		template<typename Y>
		ExWeakPtr& operator=(const ExWeakPtr<Y>& other) noexcept
		{
			ExWeakPtr(other).swap(*this);
			return *this;
		}

		template<typename Y>
		ExWeakPtr& operator=(const ExSharedPtr<Y>& shared) noexcept
		{
			ExWeakPtr(shared).swap(*this);
			return *this;
		}

		ExWeakPtr& operator=(ExWeakPtr&& other) noexcept
		{
			ExWeakPtr(std::move(other)).swap(*this);
			return *this;
		}

		template<typename Y>
		ExWeakPtr& operator=(ExWeakPtr<Y>&& other) noexcept
		{
			ExWeakPtr(std::move(other)).swap(*this);
			return *this;
		}

		// 引用计数
		long use_count() const noexcept
		{
			return ref_count_ ? ref_count_->use_count() : 0;
		}

		bool expired() const noexcept
		{
			return ref_count_ ? ref_count_->expired() : true;
		}

		// 锁定为shared_ptr
		ExSharedPtr<T> lock() const noexcept
		{
			if (expired())
				return ExSharedPtr<T>();

			try
			{
				return ExSharedPtr<T>(*this);
			}
			catch (...)
			{
				return ExSharedPtr<T>();
			}
		}

		// 重置
		void reset() noexcept
		{
			ExWeakPtr().swap(*this);
		}

		// 交换
		void swap(ExWeakPtr& other) noexcept
		{
			std::swap(ptr_, other.ptr_);
			std::swap(ref_count_, other.ref_count_);
		}

	private:
		template<typename Y> friend class ExSharedPtr;
		template<typename Y> friend class ExWeakPtr;

		T* ptr_;
		detail::RefCountBase* ref_count_;
	};

	/**
	 * @brief 独占智能指针
	 * 独占拥有对象的所有权，不可拷贝但可移动
	 */
	template<typename T, typename Deleter>
	class ExUniquePtr
	{
	public:
		using element_type = T;
		using deleter_type = Deleter;
		using pointer = T*;

		// 构造函数
		constexpr ExUniquePtr() noexcept : ptr_(nullptr) {}
		constexpr ExUniquePtr(std::nullptr_t) noexcept : ptr_(nullptr) {}

		explicit ExUniquePtr(pointer p) noexcept : ptr_(p) {}

		ExUniquePtr(pointer p, const Deleter& d) noexcept : ptr_(p), deleter_(d) {}
		ExUniquePtr(pointer p, Deleter&& d) noexcept : ptr_(p), deleter_(std::move(d)) {}

		// 移动构造函数
		ExUniquePtr(ExUniquePtr&& other) noexcept : ptr_(other.release()), deleter_(std::move(other.deleter_)) {}

		template<typename Y, typename D>
		ExUniquePtr(ExUniquePtr<Y, D>&& other) noexcept : ptr_(other.release()), deleter_(std::move(other.get_deleter()))
		{
			static_assert(std::is_convertible_v<Y*, T*>, "Y* must be convertible to T*");
		}

		// 禁用拷贝构造和拷贝赋值
		ExUniquePtr(const ExUniquePtr&) = delete;
		ExUniquePtr& operator=(const ExUniquePtr&) = delete;

		// 析构函数
		~ExUniquePtr()
		{
			if (ptr_)
				deleter_(ptr_);
		}

		// 移动赋值操作符
		ExUniquePtr& operator=(ExUniquePtr&& other) noexcept
		{
			reset(other.release());
			deleter_ = std::move(other.deleter_);
			return *this;
		}

		template<typename Y, typename D>
		ExUniquePtr& operator=(ExUniquePtr<Y, D>&& other) noexcept
		{
			static_assert(std::is_convertible_v<Y*, T*>, "Y* must be convertible to T*");
			reset(other.release());
			deleter_ = std::move(other.get_deleter());
			return *this;
		}

		ExUniquePtr& operator=(std::nullptr_t) noexcept
		{
			reset();
			return *this;
		}

		// 访问操作符
		T& operator*() const noexcept
		{
			return *ptr_;
		}

		T* operator->() const noexcept
		{
			return ptr_;
		}

		T* get() const noexcept
		{
			return ptr_;
		}

		Deleter& get_deleter() noexcept
		{
			return deleter_;
		}

		const Deleter& get_deleter() const noexcept
		{
			return deleter_;
		}

		// 类型转换
		explicit operator bool() const noexcept
		{
			return ptr_ != nullptr;
		}

		// 释放所有权
		T* release() noexcept
		{
			T* temp = ptr_;
			ptr_ = nullptr;
			return temp;
		}

		// 重置
		void reset(T* p = nullptr) noexcept
		{
			T* old = ptr_;
			ptr_ = p;
			if (old)
				deleter_(old);
		}

		// 交换
		void swap(ExUniquePtr& other) noexcept
		{
			std::swap(ptr_, other.ptr_);
			std::swap(deleter_, other.deleter_);
		}

	private:
		template<typename Y, typename D> friend class ExUniquePtr;

		T* ptr_;
		Deleter deleter_;
	};

	// 特化版本（数组）
	template<typename T, typename Deleter>
	class ExUniquePtr<T[], Deleter>
	{
	public:
		using element_type = T;
		using deleter_type = Deleter;
		using pointer = T*;

		// 构造函数
		constexpr ExUniquePtr() noexcept : ptr_(nullptr) {}
		constexpr ExUniquePtr(std::nullptr_t) noexcept : ptr_(nullptr) {}

		explicit ExUniquePtr(pointer p) noexcept : ptr_(p) {}

		ExUniquePtr(pointer p, const Deleter& d) noexcept : ptr_(p), deleter_(d) {}
		ExUniquePtr(pointer p, Deleter&& d) noexcept : ptr_(p), deleter_(std::move(d)) {}

		// 移动构造函数
		ExUniquePtr(ExUniquePtr&& other) noexcept : ptr_(other.release()), deleter_(std::move(other.deleter_)) {}

		// 禁用拷贝构造和拷贝赋值
		ExUniquePtr(const ExUniquePtr&) = delete;
		ExUniquePtr& operator=(const ExUniquePtr&) = delete;

		// 析构函数
		~ExUniquePtr()
		{
			if (ptr_)
				deleter_(ptr_);
		}

		// 移动赋值操作符
		ExUniquePtr& operator=(ExUniquePtr&& other) noexcept
		{
			reset(other.release());
			deleter_ = std::move(other.deleter_);
			return *this;
		}

		ExUniquePtr& operator=(std::nullptr_t) noexcept
		{
			reset();
			return *this;
		}

		// 数组访问操作符
		T& operator[](size_t index) const noexcept
		{
			return ptr_[index];
		}

		T* get() const noexcept
		{
			return ptr_;
		}

		Deleter& get_deleter() noexcept
		{
			return deleter_;
		}

		const Deleter& get_deleter() const noexcept
		{
			return deleter_;
		}

		// 类型转换
		explicit operator bool() const noexcept
		{
			return ptr_ != nullptr;
		}

		// 释放所有权
		T* release() noexcept
		{
			T* temp = ptr_;
			ptr_ = nullptr;
			return temp;
		}

		// 重置
		void reset(T* p = nullptr) noexcept
		{
			T* old = ptr_;
			ptr_ = p;
			if (old)
				deleter_(old);
		}

		// 交换
		void swap(ExUniquePtr& other) noexcept
		{
			std::swap(ptr_, other.ptr_);
			std::swap(deleter_, other.deleter_);
		}

	private:
		T* ptr_;
		Deleter deleter_;
	};

	// 辅助函数

	/**
	 * @brief 创建shared_ptr（推荐方式）
	 */
	template<typename T, typename... Args>
	ExSharedPtr<T> ExMakeShared(Args&&... args)
	{
		auto ref = new detail::RefCountInplace<T>(std::forward<Args>(args)...);
		return ExSharedPtr<T>(ref);
	}

	/**
	 * @brief 创建unique_ptr
	 */
	template<typename T, typename... Args>
	ExUniquePtr<T> ExMakeUnique(Args&&... args)
	{
		return ExUniquePtr<T>(new T(std::forward<Args>(args)...));
	}

	/**
	 * @brief 创建unique_ptr数组
	 */
	template<typename T>
	ExUniquePtr<T[]> ExMakeUniqueArray(size_t size)
	{
		return ExUniquePtr<T[]>(new T[size]);
	}

	// 比较操作符
	template<typename T, typename Y>
	bool operator==(const ExSharedPtr<T>& lhs, const ExSharedPtr<Y>& rhs) noexcept
	{
		return lhs.get() == rhs.get();
	}

	template<typename T>
	bool operator==(const ExSharedPtr<T>& lhs, std::nullptr_t) noexcept
	{
		return !lhs;
	}

	template<typename T>
	bool operator==(std::nullptr_t, const ExSharedPtr<T>& rhs) noexcept
	{
		return !rhs;
	}

	template<typename T, typename Y>
	bool operator!=(const ExSharedPtr<T>& lhs, const ExSharedPtr<Y>& rhs) noexcept
	{
		return !(lhs == rhs);
	}

	template<typename T>
	bool operator!=(const ExSharedPtr<T>& lhs, std::nullptr_t) noexcept
	{
		return static_cast<bool>(lhs);
	}

	template<typename T>
	bool operator!=(std::nullptr_t, const ExSharedPtr<T>& rhs) noexcept
	{
		return static_cast<bool>(rhs);
	}

	// unique_ptr比较操作符
	template<typename T, typename D, typename Y, typename E>
	bool operator==(const ExUniquePtr<T, D>& lhs, const ExUniquePtr<Y, E>& rhs) noexcept
	{
		return lhs.get() == rhs.get();
	}

	template<typename T, typename D>
	bool operator==(const ExUniquePtr<T, D>& lhs, std::nullptr_t) noexcept
	{
		return !lhs;
	}

	template<typename T, typename D>
	bool operator==(std::nullptr_t, const ExUniquePtr<T, D>& rhs) noexcept
	{
		return !rhs;
	}

	template<typename T, typename D, typename Y, typename E>
	bool operator!=(const ExUniquePtr<T, D>& lhs, const ExUniquePtr<Y, E>& rhs) noexcept
	{
		return !(lhs == rhs);
	}

	template<typename T, typename D>
	bool operator!=(const ExUniquePtr<T, D>& lhs, std::nullptr_t) noexcept
	{
		return static_cast<bool>(lhs);
	}

	template<typename T, typename D>
	bool operator!=(std::nullptr_t, const ExUniquePtr<T, D>& rhs) noexcept
	{
		return static_cast<bool>(rhs);
	}

	// 交换函数
	template<typename T>
	void swap(ExSharedPtr<T>& lhs, ExSharedPtr<T>& rhs) noexcept
	{
		lhs.swap(rhs);
	}

	template<typename T>
	void swap(ExWeakPtr<T>& lhs, ExWeakPtr<T>& rhs) noexcept
	{
		lhs.swap(rhs);
	}

	template<typename T, typename D>
	void swap(ExUniquePtr<T, D>& lhs, ExUniquePtr<T, D>& rhs) noexcept
	{
		lhs.swap(rhs);
	}

	// 类型转换函数 - 简化实现，避免访问私有成员
	template<typename T, typename Y>
	ExSharedPtr<T> ExStaticPointerCast(const ExSharedPtr<Y>& ptr) noexcept
	{
		if (!ptr) return ExSharedPtr<T>();

		try
		{
			auto p = static_cast<T*>(ptr.get());
			// 创建一个新的shared_ptr，共享相同的引用计数
			ExSharedPtr<T> result(ptr, p);
			return result;
		}
		catch (...)
		{
			return ExSharedPtr<T>();
		}
	}

	template<typename T, typename Y>
	ExSharedPtr<T> ExDynamicPointerCast(const ExSharedPtr<Y>& ptr) noexcept
	{
		if (!ptr) return ExSharedPtr<T>();

		if (auto p = dynamic_cast<T*>(ptr.get()))
		{
			try
			{
				ExSharedPtr<T> result(ptr, p);
				return result;
			}
			catch (...)
			{
				return ExSharedPtr<T>();
			}
		}
		return ExSharedPtr<T>();
	}

	template<typename T, typename Y>
	ExSharedPtr<T> ExConstPointerCast(const ExSharedPtr<Y>& ptr) noexcept
	{
		if (!ptr) return ExSharedPtr<T>();

		try
		{
			auto p = const_cast<T*>(ptr.get());
			ExSharedPtr<T> result(ptr, p);
			return result;
		}
		catch (...)
		{
			return ExSharedPtr<T>();
		}
	}

	// 类型别名（与现有ExAutoPtr兼容）
	template<typename T>
	using ExSmartPtr = ExSharedPtr<T>;

	template<typename T>
	using ExPtr = ExUniquePtr<T>;

}
