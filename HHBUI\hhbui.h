﻿/*
** 版权所有 （c） 2025 HHBUI ：https://hhbui.com/
** 保留所有权利。
** 感谢 LIB：SOUI，EXDUI，ZLIB，PUGIXML...
**
** 本软件由版权所有者和贡献者“按原样”提供
** 以及任何明示或暗示的保证，包括但不限于
** 对适销性和特定用途适用性的默示保证
** 被否认。在任何情况下，版权所有者或贡献者均不得
** 对任何直接、间接、偶然、特殊、惩戒性或
** 间接损害赔偿（包括但不限于采购
** 替代商品或服务;使用、数据或利润损失;或 BUSINESS
** 中断）无论造成何种原因和任何责任理论，无论是在
** 合同、严格责任或侵权行为（包括疏忽或其他）
** 因使用本软件而引起的任何原因，即使已告知
** 此类损坏的可能性。
*/

#pragma once

#include <inttypes.h>
#include <tchar.h>
#include <stdarg.h>
#include <stdint.h>
#include <limits.h>
#include <string>
#include <vector>
#include <unordered_map>
#include <algorithm>
#define NOMINMAX
#include <Windows.h>
#include <WindowsX.h>
#include <functional>


//引擎配置
#include "application/define.h"
#include "application/config.h"
#include "common/vstring.hpp"
#include "common/assist.h"
#include "common/coordinate.h"
#include "common/ziparchive.h"
#include "engine/base.h"
#include "engine/engine.h"
#include "engine/matrix.h"
#include "engine/animation.h"
#include "engine/renderd2d.h"
#include "engine/render_api.h"
#include "engine/dx11_shader.h"
#include "engine/dx11_buffer.h"
#include "engine/dx11_render_manager.h"
#include "engine/render_profiler.h"
#include "engine/gdi_plus_integration.h"
//界面元素相关
#include "element/resource.h"
#include "element/color.h"
#include "element/image.h"
#include "element/font.h"
#include "element/hook.h"
#include "element/path.h"
#include "element/brush.h"
#include "element/region.h"
#include "element/array.h"
#include "element/layout.h"
#include "element/wnd.h"
#include "element/canvas.h"
#include "element/control.h"
#include "element/scroll.h"
#include "element/menu.h"
#include "element/listview.h"
#include "element/droptarget.h"
//界面组件
#include "control/combutton.h"
#include "control/button.h"
#include "control/static.h"
#include "control/edit.h"
#include "control/page.h"
#include "control/item.h"
#include "control/list.h"
#include "control/badge.h"
#include "control/check.h"
#include "control/tabs.h"
#include "control/progress.h"
#include "control/slider.h"
#include "control/groupbox.h"
#include "control/hotkey.h"
#include "control/imagebox.h"
#include "control/colorpicker.h"
#include "control/combobox.h"
#include "control/treeview.h"
#include "control/table.h"
#include "control/loading.h"
#include "control/wrappanel.h"
#include "control/knobs.h"
#include "control/datebox.h"
#include "control/waveringview.h"
#include "control/tour.h"
#include "control/miniblink.h"
#include "control/chart.h"
#include "control/timeline.h"
#include "control/segmented.h"
#include "control/splashscreen.h"
#include "control/login.h"


//其它
#include <common/auto_ptr.hpp>

//增强系统组件 (C++17)
#include "common/smart_ptr.hpp"
#include "common/enhanced_mem_pool.hpp"
#include "common/enhanced_exception.hpp"
#include "common/resource_manager.hpp"


